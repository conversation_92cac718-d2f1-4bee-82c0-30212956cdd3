#include "flutter_scanner_gun_plugin.h"

#include <windows.h>
#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>
#include <flutter/standard_method_codec.h>
#include <memory>
#include <sstream>
#include <iostream>
#include <iomanip>

namespace flutter_scanner_gun
{

  // 存储原始窗口过程
  WNDPROC original_window_proc = nullptr;
  // 存储方法通道指针
  std::unique_ptr<flutter::MethodChannel<flutter::EncodableValue>> channel = nullptr;
  // 扫描数据缓冲区
  std::string scan_buffer;
  // 上次按键时间
  DWORD last_key_time = 0;
  // 超时时间（毫秒）
  const DWORD SCAN_TIMEOUT = 100;
  // 跟踪 Shift 键状态
  bool shift_pressed = false;

  // 子类化窗口过程
  LRESULT CALLBACK SubclassedWindowProc(HWND hwnd, UINT message, WPARAM wparam, LPARAM lparam)
  {
    // 处理原始输入消息
    if (message == WM_INPUT)
    {
      /// 如果不在前台，则不处理
      if (GET_RAWINPUT_CODE_WPARAM(wparam) == RIM_INPUTSINK)
      {
        return CallWindowProc(original_window_proc, hwnd, message, wparam, lparam);
      }

      UINT dwSize = 0;
      GetRawInputData((HRAWINPUT)lparam, RID_INPUT, nullptr, &dwSize, sizeof(RAWINPUTHEADER));

      if (dwSize > 0)
      {
        std::vector<BYTE> rawdata(dwSize);
        if (GetRawInputData((HRAWINPUT)lparam, RID_INPUT, rawdata.data(), &dwSize, sizeof(RAWINPUTHEADER)) == dwSize)
        {
          RAWINPUT *raw = reinterpret_cast<RAWINPUT *>(rawdata.data());

          if (raw->header.dwType == RIM_TYPEKEYBOARD)
          {
            UINT vkey = raw->data.keyboard.VKey;
            bool isShift = (vkey == VK_SHIFT || vkey == VK_LSHIFT || vkey == VK_RSHIFT);
            switch (raw->data.keyboard.Message)
            {
            case WM_KEYUP:
              if (isShift)
              {
                shift_pressed = false;
              }
              break;
            case WM_KEYDOWN:
              if (isShift)
              {
                shift_pressed = true;
                break;
              }

              DWORD current_time = GetTickCount();

              // 检查是否超时，如果超时则清空缓冲区
              if (current_time - last_key_time > SCAN_TIMEOUT && !scan_buffer.empty())
              {
                scan_buffer.clear();
              }

              last_key_time = current_time;

              // 回车键触发发送
              if (vkey == VK_RETURN && !scan_buffer.empty())
              {
                // 发送扫描结果到Flutter
                if (channel)
                {
                  channel->InvokeMethod("onScanned",
                                        std::make_unique<flutter::EncodableValue>(scan_buffer));
                }
                scan_buffer.clear();
              }
              else if (vkey >= 0x30 && vkey <= 0x39)
              {
                // 数字键 0-9 或对应的符号
                char c;
                if (shift_pressed)
                {
                  // Shift + 数字键的符号
                  const char symbols[] = ")!@#$%^&*(";
                  c = symbols[vkey - 0x30];
                }
                else
                {
                  // 普通数字
                  c = static_cast<char>(vkey);
                }
                scan_buffer += c;
              }
              else if (vkey >= 0x41 && vkey <= 0x5A)
              {
                // 字母键 A-Z
                char c = static_cast<char>(vkey);
                if (!shift_pressed)
                {
                  c = static_cast<char>(tolower(c));
                }
                scan_buffer += c;
              }
              else if (vkey == VK_SPACE)
              {
                scan_buffer += ' ';
              }
              else if (vkey == VK_OEM_MINUS)
              {
                scan_buffer += shift_pressed ? '_' : '-';
              }
              else if (vkey == VK_OEM_PLUS)
              {
                scan_buffer += shift_pressed ? '+' : '=';
              }
              else if (vkey == VK_OEM_PERIOD)
              {
                scan_buffer += '.';
              }
              else if (vkey == VK_OEM_COMMA)
              {
                scan_buffer += ',';
              }
              else if (vkey == VK_OEM_1) // 分号/冒号
              {
                scan_buffer += shift_pressed ? ':' : ';';
              }
              else if (vkey == VK_OEM_2) // 斜杠/问号
              {
                scan_buffer += shift_pressed ? '?' : '/';
              }
              else if (vkey == VK_OEM_3) // 反引号/波浪号
              {
                scan_buffer += shift_pressed ? '~' : '`';
              }
              else if (vkey == VK_OEM_4) // 左方括号/左大括号
              {
                scan_buffer += shift_pressed ? '{' : '[';
              }
              else if (vkey == VK_OEM_5) // 反斜杠/竖线
              {
                scan_buffer += shift_pressed ? '|' : '\\';
              }
              else if (vkey == VK_OEM_6) // 右方括号/右大括号
              {
                scan_buffer += shift_pressed ? '}' : ']';
              }
              else if (vkey == VK_OEM_7) // 单引号/双引号
              {
                scan_buffer += shift_pressed ? '"' : '\'';
              }
              break;
            }
          }
        }
      }
    }

    // 调用原始窗口过程
    return CallWindowProc(original_window_proc, hwnd, message, wparam, lparam);
  }

  // static
  void FlutterScannerGunPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarWindows *registrar)
  {
    channel = std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
        registrar->messenger(), "flutter_scanner_gun",
        &flutter::StandardMethodCodec::GetInstance());

    auto plugin = std::make_unique<FlutterScannerGunPlugin>(registrar);

    channel->SetMethodCallHandler(
        [plugin_pointer = plugin.get()](const auto &call, auto result)
        {
          plugin_pointer->HandleMethodCall(call, std::move(result));
        });

    registrar->AddPlugin(std::move(plugin));
  }

  FlutterScannerGunPlugin::FlutterScannerGunPlugin(flutter::PluginRegistrarWindows *registrar)
  {
    // std::cout << "[扫描枪插件] 开始初始化..." << std::endl;

    // 获取Flutter窗口句柄
    window_handle_ = registrar->GetView()->GetNativeWindow();

    // 注册原始输入设备
    RAWINPUTDEVICE rid;
    rid.usUsagePage = 0x01; // 通用桌面控制
    rid.usUsage = 0x06;     // 键盘
    rid.dwFlags = RIDEV_INPUTSINK;
    rid.hwndTarget = window_handle_;

    RegisterRawInputDevices(&rid, 1, sizeof(RAWINPUTDEVICE));

    original_window_proc = (WNDPROC)SetWindowLongPtr(window_handle_, GWLP_WNDPROC, (LONG_PTR)SubclassedWindowProc);
  }

  FlutterScannerGunPlugin::~FlutterScannerGunPlugin()
  {
    SetWindowLongPtr(window_handle_, GWLP_WNDPROC, (LONG_PTR)original_window_proc);
  }

  void FlutterScannerGunPlugin::HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result)
  {
    if (method_call.method_name().compare("initialize") == 0)
    {
      // 初始化方法，可以在这里添加额外的初始化逻辑
      result->Success(flutter::EncodableValue(true));
    }
    else
    {
      result->NotImplemented();
    }
  }

} // namespace flutter_scanner_gun
