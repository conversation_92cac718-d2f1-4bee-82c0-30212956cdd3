#include "flutter_scanner_gun_plugin.h"

#include <windows.h>
#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>
#include <flutter/standard_method_codec.h>
#include <memory>
#include <sstream>

namespace flutter_scanner_gun
{

  // 存储原始窗口过程
  WNDPROC original_window_proc = nullptr;
  // 存储方法通道指针
  std::unique_ptr<flutter::MethodChannel<flutter::EncodableValue>> channel = nullptr;
  // 扫描数据缓冲区
  std::string scan_buffer;
  // 上次按键时间
  DWORD last_key_time = 0;
  // 超时时间（毫秒）
  const DWORD SCAN_TIMEOUT = 100;

  // 子类化窗口过程
  LRESULT CALLBACK SubclassedWindowProc(HWND hwnd, UINT message, WPARAM wparam, LPARAM lparam)
  {
    // 处理原始输入消息
    if (message == WM_INPUT)
    {
      UINT dwSize = 0;
      GetRawInputData((HRAWINPUT)lparam, RID_INPUT, nullptr, &dwSize, sizeof(RAWINPUTHEADER));

      if (dwSize > 0)
      {
        std::vector<BYTE> rawdata(dwSize);
        if (GetRawInputData((HRAWINPUT)lparam, RID_INPUT, rawdata.data(), &dwSize, sizeof(RAWINPUTHEADER)) == dwSize)
        {
          RAWINPUT *raw = reinterpret_cast<RAWINPUT *>(rawdata.data());

          if (raw->header.dwType == RIM_TYPEKEYBOARD)
          {
            // 只处理按键按下事件
            if (raw->data.keyboard.Message == WM_KEYDOWN || raw->data.keyboard.Message == WM_SYSKEYDOWN)
            {
              DWORD current_time = GetTickCount();

              // 检查是否超时，如果超时则清空缓冲区
              if (current_time - last_key_time > SCAN_TIMEOUT && !scan_buffer.empty())
              {
                scan_buffer.clear();
              }

              last_key_time = current_time;

              // 处理按键
              UINT vkey = raw->data.keyboard.VKey;

              // 回车键触发发送
              if (vkey == VK_RETURN && !scan_buffer.empty())
              {
                // 发送扫描结果到Flutter
                if (channel)
                {
                  channel->InvokeMethod("onScanned",
                                        std::make_unique<flutter::EncodableValue>(scan_buffer));
                }
                scan_buffer.clear();
              }
              else if (vkey >= 0x30 && vkey <= 0x39)
              {
                // 数字键 0-9
                scan_buffer += static_cast<char>(vkey);
              }
              else if (vkey >= 0x41 && vkey <= 0x5A)
              {
                // 字母键 A-Z
                bool is_shift = (GetKeyState(VK_SHIFT) & 0x8000) != 0;
                char c = static_cast<char>(vkey);
                if (!is_shift)
                {
                  c = static_cast<char>(tolower(c));
                }
                scan_buffer += c;
              }
              else if (vkey == VK_OEM_MINUS)
              {
                scan_buffer += '-';
              }
              else if (vkey == VK_OEM_PLUS)
              {
                scan_buffer += '+';
              }
              // 可以添加更多特殊字符处理
            }
          }
        }
      }
    }

    // 调用原始窗口过程
    return CallWindowProc(original_window_proc, hwnd, message, wparam, lparam);
  }

  // static
  void FlutterScannerGunPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarWindows *registrar)
  {
    channel = std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
        registrar->messenger(), "flutter_scanner_gun",
        &flutter::StandardMethodCodec::GetInstance());

    auto plugin = std::make_unique<FlutterScannerGunPlugin>(registrar);

    channel->SetMethodCallHandler(
        [plugin_pointer = plugin.get()](const auto &call, auto result)
        {
          plugin_pointer->HandleMethodCall(call, std::move(result));
        });

    registrar->AddPlugin(std::move(plugin));
  }

  FlutterScannerGunPlugin::FlutterScannerGunPlugin(flutter::PluginRegistrarWindows *registrar)
  {
    // 获取Flutter窗口句柄
    HWND hwnd = GetAncestor(FindWindowW(L"FLUTTER_RUNNER_WIN32_WINDOW", nullptr), GA_ROOT);
    if (hwnd)
    {
      // 注册原始输入设备
      RAWINPUTDEVICE rid;
      rid.usUsagePage = 0x01; // 通用桌面控制
      rid.usUsage = 0x06;     // 键盘
      rid.dwFlags = RIDEV_INPUTSINK;
      rid.hwndTarget = hwnd;
      RegisterRawInputDevices(&rid, 1, sizeof(RAWINPUTDEVICE));

      // 子类化窗口过程
      original_window_proc = (WNDPROC)SetWindowLongPtr(hwnd, GWLP_WNDPROC, (LONG_PTR)SubclassedWindowProc);
    }
  }

  FlutterScannerGunPlugin::~FlutterScannerGunPlugin()
  {
    // 我们无法在这里访问registrar，因为它可能已经被销毁
    // 但我们可以使用保存的窗口句柄或再次查找窗口
    
    // 如果我们想要更可靠，可以在构造函数中保存hwnd到类成员变量
    // 这里简单起见，仍然使用FindWindowW
    HWND hwnd = GetAncestor(FindWindowW(L"FLUTTER_RUNNER_WIN32_WINDOW", nullptr), GA_ROOT);
    if (hwnd && original_window_proc)
    {
      SetWindowLongPtr(hwnd, GWLP_WNDPROC, (LONG_PTR)original_window_proc);
    }
  }

  void FlutterScannerGunPlugin::HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result)
  {
    if (method_call.method_name().compare("initialize") == 0)
    {
      // 初始化方法，可以在这里添加额外的初始化逻辑
      result->Success(flutter::EncodableValue(true));
    }
    else
    {
      result->NotImplemented();
    }
  }

} // namespace flutter_scanner_gun







