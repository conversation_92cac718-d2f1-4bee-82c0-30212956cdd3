# Flutter Scanner Gun

一个专为 Windows 平台设计的 Flutter 扫码枪插件，通过监听原始键盘输入来避免输入法干扰，确保扫码数据的准确接收。

## 快速开始

### 1. 安装

在`pubspec.yaml`中添加依赖：

```yaml
dependencies:
  flutter_scanner_gun: ^1.0.0
```

然后运行：

```bash
flutter pub get
```

### 2. 最简示例

```dart
import 'package:flutter/material.dart';
import 'package:flutter_scanner_gun/flutter_scanner_gun.dart';
import 'dart:async';

void main() => runApp(MyApp());

class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String scannedData = '等待扫码...';
  StreamSubscription<String>? _scannerSubscription;

  @override
  void initState() {
    super.initState();
    _initScanner();
  }

  @override
  void dispose() {
    _scannerSubscription?.cancel();
    super.dispose();
  }

  Future<void> _initScanner() async {
    await FlutterScannerGun.init();
    _scannerSubscription = FlutterScannerGun.onScannedData.listen((data) {
      setState(() => scannedData = data);
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: Text('扫码枪测试')),
        body: Center(
          child: Text(scannedData, style: TextStyle(fontSize: 24)),
        ),
      ),
    );
  }
}
```

## API 参考

### FlutterScannerGun

静态类，提供扫码枪功能的访问接口。

#### 静态方法

##### `static Future<void> init()`

初始化扫码枪插件。

**返回值**: `Future<void>` - 初始化完成的 Future

**示例**:

```dart
await FlutterScannerGun.init();
```

##### `static Stream<String> get onScannedData`

获取扫码数据流。

**返回值**: `Stream<String>` - 扫码数据的广播流

**示例**:

```dart
StreamSubscription<String> subscription = FlutterScannerGun.onScannedData.listen((data) {
  print('收到扫码数据: $data');
});
```

**注意**: 记得在不需要时取消订阅以避免内存泄漏：

```dart
subscription?.cancel();
```

## 技术原理

### Windows 实现

插件使用 Windows Raw Input API (`RegisterRawInputDevices`) 来监听键盘输入：

1. **注册原始输入设备**: 监听所有键盘设备的输入
2. **处理 WM_INPUT 消息**: 捕获原始键盘事件（按下和释放）
3. **Shift 状态跟踪**: 实时跟踪 Shift 键状态，确保符号输入正确
4. **按键释放处理**: 只在按键释放时处理字符，避免输入法干扰
5. **扫描码转换**: 将虚拟键码转换为可读字符
6. **数据缓冲**: 收集扫码数据直到收到回车键
7. **超时检测**: 100ms 超时自动清空缓冲区
8. **数据发送**: 通过 Method Channel 将数据发送到 Flutter

### 关键特性

- **智能输入法绕过**: 通过处理按键释放事件完全避免输入法干扰
- **完整字符支持**:
  - 数字键：`0-9` 和 Shift 组合 `!@#$%^&*()`
  - 字母键：`a-z` 和 `A-Z`（支持 Shift 大小写）
  - 特殊符号：`-_=+[]{}\\|;:'",.<>/?~` 等
  - 空格和其他控制字符
- **状态管理**: 独立的 Shift 状态跟踪，不依赖系统状态
- **超时处理**: 自动检测扫码序列的开始和结束
- **调试支持**: 详细的控制台输出，显示每个按键的处理过程

## 系统要求

- Windows 10 或更高版本
- Flutter 3.0 或更高版本
- 支持 USB HID 的扫码枪设备
- Visual Studio 2019 或更高版本（用于编译）

## 支持的字符

插件支持以下字符的输入：

### 基本字符

- **数字**: `0123456789`
- **字母**: `abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ`
- **空格**: ` `

### 特殊符号

- **标点符号**: `.,;:'"`
- **数学符号**: `+-=`
- **括号**: `()[]{}`
- **斜杠**: `/\|`
- **其他**: `~!@#$%^&*_?<>`

## 注意事项

1. **窗口焦点**: 确保 Flutter 应用窗口处于焦点状态
2. **设备兼容性**: 确保扫码枪设置为 USB HID 键盘模式
3. **输入法状态**: 建议切换到英文输入法以获得最佳效果
4. **扫码枪设置**: 确保扫码枪在扫码后发送回车键（CR/LF）
5. **调试模式**: 开发时可查看控制台输出了解按键处理过程
6. **内存管理**: 记得取消 Stream 订阅以避免内存泄漏

## 许可证

MIT License