#include "include/flutter_scanner_gun/flutter_scanner_gun_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "flutter_scanner_gun_plugin.h"

#include <windows.h>
#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>
#include <flutter/standard_method_codec.h>
#include <memory>
#include <sstream>

void FlutterScannerGunPluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  flutter_scanner_gun::FlutterScannerGunPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
