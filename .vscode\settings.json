{"files.associations": {"charconv": "cpp", "chrono": "cpp", "format": "cpp", "functional": "cpp", "xlocale": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "algorithm": "cpp", "any": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "deque": "cpp", "exception": "cpp", "forward_list": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "list": "cpp", "locale": "cpp", "map": "cpp", "memory": "cpp", "new": "cpp", "optional": "cpp", "ostream": "cpp", "ratio": "cpp", "set": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "string": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "utility": "cpp", "variant": "cpp", "vector": "cpp", "xfacet": "cpp", "xhash": "cpp", "xiosbase": "cpp", "xlocbuf": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xstring": "cpp", "xtr1common": "cpp", "xtree": "cpp", "xutility": "cpp"}}