# Flutter Scanner Gun

一个专为Windows平台设计的Flutter扫码枪插件，通过监听原始键盘输入来避免输入法干扰，确保扫码数据的准确接收。

## 快速开始

### 1. 安装

在`pubspec.yaml`中添加依赖：

```yaml
dependencies:
  flutter_scanner_gun: ^1.0.0
```

然后运行：
```bash
flutter pub get
```

### 2. 最简示例

```dart
import 'package:flutter/material.dart';
import 'package:flutter_scanner_gun/flutter_scanner_gun.dart';

void main() => runApp(MyApp());

class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final FlutterScannerGun scanner = FlutterScannerGun();
  String scannedData = '等待扫码...';

  @override
  void initState() {
    super.initState();
    _initScanner();
  }

  Future<void> _initScanner() async {
    await scanner.init();
    scanner.onScannedData.listen((data) {
      setState(() => scannedData = data);
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: Text('扫码枪测试')),
        body: Center(
          child: Text(scannedData, style: TextStyle(fontSize: 24)),
        ),
      ),
    );
  }
}
```

## 详细使用方法

### 1. 初始化插件

```dart
import 'package:flutter_scanner_gun/flutter_scanner_gun.dart';

final scanner = FlutterScannerGun();

// 初始化扫码器
await scanner.init();
```

### 2. 监听扫码数据

```dart
// 监听扫码数据流
scanner.onScannedData.listen((data) {
  print('扫码数据: $data');
  // 处理扫码数据
});
```

### 3. 完整示例

```dart
import 'package:flutter/material.dart';
import 'package:flutter_scanner_gun/flutter_scanner_gun.dart';

class ScannerPage extends StatefulWidget {
  @override
  _ScannerPageState createState() => _ScannerPageState();
}

class _ScannerPageState extends State<ScannerPage> {
  final FlutterScannerGun scanner = FlutterScannerGun();
  String lastScannedData = '';

  @override
  void initState() {
    super.initState();
    _initScanner();
  }

  Future<void> _initScanner() async {
    try {
      await scanner.init();
      
      // 监听扫码数据
      scanner.onScannedData.listen((data) {
        setState(() {
          lastScannedData = data;
        });
      });
    } catch (e) {
      print('初始化失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('扫码器')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('最后扫码数据:'),
            Text(lastScannedData, style: TextStyle(fontSize: 24)),
          ],
        ),
      ),
    );
  }
}
```

## 技术原理

### Windows实现

插件使用Windows Raw Input API (`RegisterRawInputDevices`) 来监听键盘输入：

1. **注册原始输入设备**: 监听所有键盘设备的输入
2. **处理WM_INPUT消息**: 捕获原始键盘事件（按下和释放）
3. **Shift状态跟踪**: 实时跟踪Shift键状态，确保符号输入正确
4. **按键释放处理**: 只在按键释放时处理字符，避免输入法干扰
5. **扫描码转换**: 将虚拟键码转换为可读字符
6. **数据缓冲**: 收集扫码数据直到收到回车键
7. **超时检测**: 100ms超时自动清空缓冲区
8. **数据发送**: 通过Method Channel将数据发送到Flutter

### 关键特性

- **智能输入法绕过**: 通过处理按键释放事件完全避免输入法干扰
- **完整字符支持**:
  - 数字键：`0-9` 和 Shift组合 `!@#$%^&*()`
  - 字母键：`a-z` 和 `A-Z`（支持Shift大小写）
  - 特殊符号：`-_=+[]{}\\|;:'",.<>/?~` 等
  - 空格和其他控制字符
- **状态管理**: 独立的Shift状态跟踪，不依赖系统状态
- **超时处理**: 自动检测扫码序列的开始和结束
- **调试支持**: 详细的控制台输出，显示每个按键的处理过程

## 系统要求

- Windows 10 或更高版本
- Flutter 3.0 或更高版本
- 支持USB HID的扫码枪设备
- Visual Studio 2019 或更高版本（用于编译）

## 支持的字符

插件支持以下字符的输入：

### 基本字符
- **数字**: `**********`
- **字母**: `abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ`
- **空格**: ` `

### 特殊符号
- **标点符号**: `.,;:'"`
- **数学符号**: `+-=`
- **括号**: `()[]{}`
- **斜杠**: `/\|`
- **其他**: `~!@#$%^&*_?<>`

## 许可证

MIT License

## 更新日志

### v1.0.0
- 初始版本发布
- 支持Windows平台扫码枪输入
- 实现Raw Input API监听
- 支持完整字符集和特殊符号
- 智能输入法绕过机制
- 详细调试输出支持

