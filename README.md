# Flutter Scanner Gun

一个用于Windows平台的Flutter扫码枪插件，通过监听原始键盘输入来避免输入法干扰。

## 功能特性

- 🎯 **原始输入监听**: 使用Windows Raw Input API直接监听键盘输入
- 🚫 **避免输入法干扰**: 绕过系统输入法，直接获取扫码枪的原始数据
- ⚡ **实时响应**: 通过Stream实时接收扫码数据
- 🔧 **简单易用**: 提供简洁的API接口

## 安装

在`pubspec.yaml`中添加依赖：

```yaml
dependencies:
  flutter_scanner_gun: ^1.0.0
```

## 使用方法

### 1. 初始化插件

```dart
import 'package:flutter_scanner_gun/flutter_scanner_gun.dart';

final scanner = FlutterScannerGun();

// 初始化扫码器
await scanner.init();
```

### 2. 监听扫码数据

```dart
// 监听扫码数据流
scanner.onScannedData.listen((data) {
  print('扫码数据: $data');
  // 处理扫码数据
});
```

### 3. 完整示例

```dart
import 'package:flutter/material.dart';
import 'package:flutter_scanner_gun/flutter_scanner_gun.dart';

class ScannerPage extends StatefulWidget {
  @override
  _ScannerPageState createState() => _ScannerPageState();
}

class _ScannerPageState extends State<ScannerPage> {
  final FlutterScannerGun scanner = FlutterScannerGun();
  String lastScannedData = '';

  @override
  void initState() {
    super.initState();
    _initScanner();
  }

  Future<void> _initScanner() async {
    try {
      await scanner.init();
      
      // 监听扫码数据
      scanner.onScannedData.listen((data) {
        setState(() {
          lastScannedData = data;
        });
      });
    } catch (e) {
      print('初始化失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('扫码器')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('最后扫码数据:'),
            Text(lastScannedData, style: TextStyle(fontSize: 24)),
          ],
        ),
      ),
    );
  }
}
```

## 技术原理

### Windows实现

插件使用Windows Raw Input API (`RegisterRawInputDevices`) 来监听键盘输入：

1. **注册原始输入设备**: 监听所有键盘设备的输入
2. **处理WM_INPUT消息**: 捕获原始键盘事件
3. **扫描码转换**: 将扫描码转换为可读字符
4. **数据缓冲**: 收集扫码数据直到收到回车键
5. **数据发送**: 通过Method Channel将数据发送到Flutter

### 关键特性

- **绕过输入法**: 直接处理原始键盘输入，不受输入法影响
- **超时处理**: 自动检测扫码序列的开始和结束
- **字符映射**: 支持数字、字母和常用特殊字符

## 系统要求

- Windows 10 或更高版本
- Flutter 3.0 或更高版本
- 支持USB HID的扫码枪设备

## 注意事项

1. **窗口焦点**: 确保Flutter应用窗口处于焦点状态
2. **设备兼容性**: 确保扫码枪设置为USB HID键盘模式
3. **权限**: 应用需要键盘输入权限

## 故障排除

### 常见问题

1. **无法接收数据**
   - 检查窗口是否处于焦点状态
   - 确认扫码枪已正确连接
   - 验证扫码枪设置为键盘模式

2. **数据不完整**
   - 检查扫码枪是否发送回车键
   - 确认扫码枪设置正确

3. **初始化失败**
   - 检查Windows版本兼容性
   - 确认应用有足够权限

## 许可证

MIT License

