# Flutter Scanner Gun 使用说明

## 概述

Flutter Scanner Gun 是一个专门为Windows平台设计的Flutter插件，用于监听扫码枪的输入。它通过Windows Raw Input API直接监听键盘输入，避免了输入法的干扰。

## 快速开始

### 1. 添加依赖

在 `pubspec.yaml` 中添加：

```yaml
dependencies:
  flutter_scanner_gun: ^1.0.0
```

### 2. 基本使用

```dart
import 'package:flutter_scanner_gun/flutter_scanner_gun.dart';

class ScannerDemo extends StatefulWidget {
  @override
  _ScannerDemoState createState() => _ScannerDemoState();
}

class _ScannerDemoState extends State<ScannerDemo> {
  final FlutterScannerGun scanner = FlutterScannerGun();
  String scannedData = '';

  @override
  void initState() {
    super.initState();
    _initScanner();
  }

  Future<void> _initScanner() async {
    try {
      await scanner.init();
      
      // 监听扫码数据
      scanner.onScannedData.listen((data) {
        setState(() {
          scannedData = data;
        });
        print('收到扫码数据: $data');
      });
    } catch (e) {
      print('初始化失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('扫码器演示')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('扫码数据:', style: TextStyle(fontSize: 18)),
            SizedBox(height: 10),
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                scannedData.isEmpty ? '等待扫码...' : scannedData,
                style: TextStyle(fontSize: 24, fontFamily: 'monospace'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```

## API 参考

### FlutterScannerGun 类

#### 方法

##### `init()`
初始化扫码器。

```dart
Future<void> init()
```

**返回值**: `Future<void>`

**说明**: 初始化Windows Raw Input设备监听。必须在监听扫码数据之前调用。

#### 属性

##### `onScannedData`
扫码数据流。

```dart
Stream<String> get onScannedData
```

**返回值**: `Stream<String>`

**说明**: 返回一个Stream，用于监听扫码数据。每当扫码枪完成一次扫码（发送回车键）时，会发出一个包含扫码数据的String事件。

## 工作原理

### Windows 实现细节

1. **注册原始输入设备**
   ```cpp
   RegisterRawInputDevices(&rid_, 1, sizeof(RAWINPUTDEVICE))
   ```

2. **监听WM_INPUT消息**
   - 通过窗口过程钩子捕获WM_INPUT消息
   - 解析原始键盘数据

3. **扫描码转换**
   - 将键盘扫描码转换为ASCII字符
   - 支持数字、字母和常用特殊字符

4. **数据缓冲**
   - 收集连续的键盘输入
   - 检测扫码序列的开始和结束（通过时间间隔）

5. **数据发送**
   - 通过Method Channel将数据发送到Flutter
   - 使用Stream提供实时数据流

### 字符映射

插件支持以下字符的扫描码转换：

- **数字**: 0-9
- **字母**: A-Z, a-z
- **特殊字符**: 空格、连字符、下划线等

## 配置要求

### 扫码枪设置

1. **USB HID模式**: 确保扫码枪设置为USB HID键盘模式
2. **回车键**: 扫码枪应该在扫码完成后发送回车键
3. **字符集**: 确保扫码枪使用标准ASCII字符集

### 系统要求

- Windows 10 或更高版本
- Flutter 3.0 或更高版本
- 支持USB HID的设备

## 故障排除

### 常见问题

#### 1. 无法接收扫码数据

**可能原因**:
- 窗口未处于焦点状态
- 扫码枪未正确连接
- 扫码枪未设置为键盘模式

**解决方案**:
- 确保Flutter应用窗口处于活动状态
- 检查扫码枪连接和驱动
- 验证扫码枪设置

#### 2. 数据不完整

**可能原因**:
- 扫码枪未发送回车键
- 扫码枪设置不正确

**解决方案**:
- 检查扫码枪配置
- 确保扫码枪在扫码完成后发送回车键

#### 3. 初始化失败

**可能原因**:
- Windows版本不兼容
- 权限不足

**解决方案**:
- 升级到Windows 10或更高版本
- 以管理员身份运行应用

### 调试技巧

1. **启用调试输出**
   ```dart
   scanner.onScannedData.listen((data) {
     print('调试 - 扫码数据: $data');
     // 处理数据
   });
   ```

2. **检查初始化状态**
   ```dart
   try {
     await scanner.init();
     print('扫码器初始化成功');
   } catch (e) {
     print('扫码器初始化失败: $e');
   }
   ```

## 最佳实践

1. **错误处理**: 始终使用try-catch包装初始化代码
2. **资源管理**: 在dispose时取消Stream订阅
3. **用户体验**: 提供清晰的用户反馈和错误信息
4. **测试**: 使用真实的扫码枪设备进行测试

## 示例项目

完整的使用示例请参考 `example/lib/main.dart` 文件。 