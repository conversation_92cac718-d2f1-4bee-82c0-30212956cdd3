//
//  Generated file. Do not edit.
//

// clang-format off

#import "GeneratedPluginRegistrant.h"

#if __has_include(<flutter_scanner_gun/FlutterScannerGunPlugin.h>)
#import <flutter_scanner_gun/FlutterScannerGunPlugin.h>
#else
@import flutter_scanner_gun;
#endif

#if __has_include(<integration_test/IntegrationTestPlugin.h>)
#import <integration_test/IntegrationTestPlugin.h>
#else
@import integration_test;
#endif

@implementation GeneratedPluginRegistrant

+ (void)registerWithRegistry:(NSObject<FlutterPluginRegistry>*)registry {
  [FlutterScannerGunPlugin registerWithRegistrar:[registry registrarForPlugin:@"FlutterScannerGunPlugin"]];
  [IntegrationTestPlugin registerWithRegistrar:[registry registrarForPlugin:@"IntegrationTestPlugin"]];
}

@end
