import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'flutter_scanner_gun_platform_interface.dart';

/// An implementation of [FlutterScannerGunPlatform] that uses method channels.
class MethodChannelFlutterScannerGun extends FlutterScannerGunPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('flutter_scanner_gun');

  /// Stream controller for scanned data
  final StreamController<String> _scannedDataController =
      StreamController<String>.broadcast();

  MethodChannelFlutterScannerGun() {
    methodChannel.setMethodCallHandler(_handleMethodCall);
  }

  @override
  Future<void> init() async {
    await methodChannel.invokeMethod<void>('initialize');
  }

  @override
  Stream<String> get onScannedData => _scannedDataController.stream;

  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onScanned':
        final String scannedData = call.arguments as String;
        _scannedDataController.add(scannedData);
        break;
      default:
        throw PlatformException(
          code: 'Unimplemented',
          details:
              'flutter_scanner_gun for Windows doesn\'t implement \'${call.method}\'',
        );
    }
  }
}
