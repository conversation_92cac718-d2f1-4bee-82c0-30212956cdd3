import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'flutter_scanner_gun_platform_interface.dart';

/// An implementation of [FlutterScannerGunPlatform] that uses method channels.
class MethodChannelFlutterScannerGun extends FlutterScannerGunPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('flutter_scanner_gun');

  MethodChannelFlutterScannerGun() {
    methodChannel.setMethodCallHandler(_handleMethodCall);
  }

  @override
  Future<void> init() async {
    await methodChannel.invokeMethod<void>('init');
  }

  @override
  Stream<String> get onScannedData {
    throw UnimplementedError('onScannedData has not been implemented.');
  }

  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      default:
        throw PlatformException(
          code: 'Unimplemented',
          details:
              'flutter_scanner_gun for Windows doesn\'t implement \'${call.method}\'',
        );
    }
  }
}
