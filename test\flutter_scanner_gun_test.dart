// import 'package:flutter_test/flutter_test.dart';
// import 'package:flutter_scanner_gun/flutter_scanner_gun.dart';
// import 'package:flutter_scanner_gun/flutter_scanner_gun_platform_interface.dart';
// import 'package:flutter_scanner_gun/flutter_scanner_gun_method_channel.dart';
// import 'package:plugin_platform_interface/plugin_platform_interface.dart';

// class MockFlutterScannerGunPlatform
//     with MockPlatformInterfaceMixin
//     implements FlutterScannerGunPlatform {
//   @override
//   Future<void> init() => Future.value();

//   @override
//   Stream<String> get onScannedData => Stream.value('test');
// }

// void main() {
//   final FlutterScannerGunPlatform initialPlatform =
//       FlutterScannerGunPlatform.instance;

//   test('$MethodChannelFlutterScannerGun is the default instance', () {
//     expect(initialPlatform, isInstanceOf<MethodChannelFlutterScannerGun>());
//   });

//   test('getPlatformVersion', () async {
//     FlutterScannerGun flutterScannerGunPlugin = FlutterScannerGun();
//     MockFlutterScannerGunPlatform fakePlatform =
//         MockFlutterScannerGunPlatform();
//     FlutterScannerGunPlatform.instance = fakePlatform;

//     await flutterScannerGunPlugin.init();
//   });
// }
