import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'flutter_scanner_gun_method_channel.dart';

abstract class FlutterScannerGunPlatform extends PlatformInterface {
  /// Constructs a FlutterScannerGunPlatform.
  FlutterScannerGunPlatform() : super(token: _token);

  static final Object _token = Object();

  static FlutterScannerGunPlatform _instance = MethodChannelFlutterScannerGun();

  /// The default instance of [FlutterScannerGunPlatform] to use.
  ///
  /// Defaults to [MethodChannelFlutterScannerGun].
  static FlutterScannerGunPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [FlutterScannerGunPlatform] when
  /// they register themselves.
  static set instance(FlutterScannerGunPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<void> init() {
    throw UnimplementedError('init() has not been implemented.');
  }

  /// Stream of scanned data
  Stream<String> get onScannedData {
    throw UnimplementedError('onScannedData has not been implemented.');
  }
}
