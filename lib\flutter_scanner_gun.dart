import 'dart:async';
import 'package:flutter/foundation.dart';

import 'flutter_scanner_gun_platform_interface.dart';

class FlutterScannerGun with ChangeNotifier {
  /// Initialize the scanner
  Future<void> init() async {
    await FlutterScannerGunPlatform.instance.init();
  }

  /// Stream of scanned data
  Stream<String> get onScannedData =>
      FlutterScannerGunPlatform.instance.onScannedData;
}
