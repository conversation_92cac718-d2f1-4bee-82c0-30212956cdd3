#ifndef FLUTTER_PLUGIN_FLUTTER_SCANNER_GUN_PLUGIN_H_
#define FLUTTER_PLUGIN_FLUTTER_SCANNER_GUN_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace flutter_scanner_gun
{

  class FlutterScannerGunPlugin : public flutter::Plugin
  {
  public:
    static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

    FlutterScannerGunPlugin(flutter::PluginRegistrarWindows *registrar);

    virtual ~FlutterScannerGunPlugin();

    // Disallow copy and assign.
    FlutterScannerGunPlugin(const FlutterScannerGunPlugin &) = delete;
    FlutterScannerGunPlugin &operator=(const FlutterScannerGunPlugin &) = delete;

    // Called when a method is called on this plugin's channel from Dart.
    void HandleMethodCall(
        const flutter::MethodCall<flutter::EncodableValue> &method_call,
        std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);

  private:
    // 保存窗口句柄
    HWND window_handle_ = nullptr;
  };

} // namespace flutter_scanner_gun

#endif // FLUTTER_PLUGIN_FLUTTER_SCANNER_GUN_PLUGIN_H_
